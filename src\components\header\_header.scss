.header-wrapper {
    $this: &;

    display: block;
    width: 100%;

    &__container {
        @extend %container-fluid;
        margin: 0 auto;
        padding: 0;
        position: relative;

        @include breakpoint(medium down) {
            max-width: 100%;
        }
    }
}

.header {
    $this: &;

    @include trs(background);
    opacity: 1;
    padding: 15px 0 25px;
    z-index: 20;

    .home-page & {
        padding: 25px 0 0 0;

        @include breakpoint(small down) {
            padding: 25px 0;
        }
    }

    @include breakpoint(medium down) {
        padding: 15px 0;

        &.js-fixed-el-abs {
            left: 0;
            transform: none;
        }
    }

    @include breakpoint(small down) {
        padding: 14px 0 30px;
    }

    &__inner {
        align-items: flex-start;
        display: flex;
        margin: 0 auto;
        max-width: 1460px;
        min-height: 80px;
        padding: 0 30px 13px 50px;

        @include breakpoint(medium down) {
            min-height: 62px;
            padding: 0 30px 20px;
        }

        @include breakpoint(small down) {
            min-height: 62px;
            padding: 0 20px;
            position: relative;
        }
    }

    &__logo {
        @include trs;
        align-content: center;
        align-items: center;
        display: flex;
        flex: 1 0 250px;
        min-height: 80px;
        padding: 0;

        @include breakpoint(medium down) {
            flex-basis: 152px;
            min-height: 62px;
        }

        @include breakpoint(small down) {
            @include absolute(50%, null, null, 50%);
            @include size(152px, 62px);
            min-height: 45px;
            transform: translate(-50%, -50%);
        }

        #{$this}:not(.js-fixed-el) & {
            body:not(.is-mnv-opened) .has-page-image &,
            body:not(.is-mnv-opened).home-page &,
            body:not(.is-mnv-opened).home-hospital-page & {
                a.logo {
                    color: $color-white;
                }
            }
        }
    }

    &__components {
        align-items: center;
        display: flex;
        flex: 1 1 100%;
        flex-wrap: wrap;
        justify-content: flex-end;

        @include breakpoint(medium down) {
            align-items: center;
        }

        @include breakpoint(small down) {
            justify-content: space-between;
        }
    }

    &__actions {
        align-items: flex-start;
        display: flex;
        flex-direction: column;
        height: 80px;
        justify-content: center;
        margin-right: 35px;

        @include breakpoint(medium down) {
            flex-direction: row;
            height: auto;
        }

        @include breakpoint(small down) {
            display: none;
        }

        .btn {
            border: none;
            color: $color-black;
            font-size: 1.5rem;
            margin: 5px 0;
            min-height: auto;
            padding: 0;

            @include breakpoint(medium down) {
                align-items: center;
                flex-direction: column;
                margin: 0 20px;
            }

            @include on-event() {
                background-color: transparent;
                color: $color-black;
            }

            span.btn__svg {
                width: 1.2em;

                @include breakpoint(medium down) {
                    margin-bottom: 5px;
                }

                svg {
                    fill: var(--color-1--1);
                }
            }
        }
    }

    &__search {
        @include breakpoint(medium down) {
            order: 2;
        }
    }

    &__lang {
        @include size(54px, 80px);
        align-items: center;
        display: flex;
        margin-right: 35px;

        @include breakpoint(medium down) {
            display: none;
        }
    }

    &__nav {
        align-items: center;
        display: flex;
        flex-grow: 1;
        justify-content: center;
        width: 1%;

        @include breakpoint(medium down) {
            display: none;
        }
    }

    &__nav-toggle {
        @include breakpoint(medium down) {
            order: 3;
        }
    }

    &.has-nav-bottom {
        @include breakpoint(large) {
            padding-bottom: 0;
        }

        #{$this}__inner {
            @include breakpoint(large) {
                flex-wrap: wrap;
            }
        }

        #{$this}__components {
            @include breakpoint(large) {
                flex-basis: calc(100% - 404px);
            }
        }

        #{$this}__nav {
            @include breakpoint(large) {

                background-color: var(--color-1--1);
                margin: 25px 0 0;
                min-height: 80px;
                position: relative;
                width: 100%;

                &::before,
                &::after {
                    @include absolute(0, null, 0, null);
                    background-color: var(--color-1--1);
                    content: '';
                    width: 1000vh;
                    z-index: -1;
                }

                &::before {
                    left: 0;
                }

                &::after {
                    right: 0;
                }
            }

            @include breakpoint(medium down) {
                display: none;
            }
        }
    }

    &.js-fixed-el {
        background-color: $color-white;

        @include breakpoint(medium) {
            padding: 10px 0;

            #{$this}__inner {
                min-height: 45px;
            }

            #{$this}__logo {
                flex-basis: 272px;
                min-height: 45px;
            }

            #{$this}__actions,
            #{$this}__lang {
                height: auto;
            }

            .lang {
                &__toggle {
                    height: 45px;
                }

                &__block {
                    top: 100%;
                }
            }

            .main-nav-toggle,
            .search__toggle {
                @include size(45px);
                min-height: 45px;
            }
        }

        &.has-nav-bottom {
            @include breakpoint(large) {
                padding-bottom: 0;

                #{$this}__nav {
                    margin-top: 10px;
                    min-height: 55px;
                }
            }
        }
    }

    &:not(.js-fixed-el).has-logo-center {
        #{$this}__logo {
            @include absolute(20px, 50%);
            transform: translate(50%, 100%);

            @include breakpoint(medium down) {
                top: 125px;
            }

            @include breakpoint(small down) {
                top: 75px;
            }
        }

        #{$this}__actions {
            display: flex;
            margin-right: auto;

            @include breakpoint(small down) {
                height: 45px;
            }
        }
    }
}
