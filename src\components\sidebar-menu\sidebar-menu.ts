import { getViewport } from '@core/utils/responsive.utils';
import { addClass, hasClass, removeClass } from '@core/utils/class.utils';
import { getUniqId } from '@core/utils/common.utils';

// eslint-disable-next-line sonarjs/cognitive-complexity
const createSidebarMenu = (): void => {
    const sideBarMenu = document.querySelector('.sidebar-menu') as HTMLElement;
    const sideBarMenuDesktop = document.querySelector('.sidebar-menu.is-desktop') as HTMLElement;

    // Dropdown is opened on desktop only
    if (sideBarMenu) {
        (getViewport() === 'mobile' ? removeClass : addClass)(sideBarMenu, 'is-open');
    }

    const sideBarMenuListDesktop = document.querySelector('.sidebar-menu__list.is-desktop') as HTMLElement;
    const sideBarMenuListMobile = document.querySelector('.sidebar-menu__list.is-mobile') as HTMLElement;

    if (sideBarMenuListDesktop && sideBarMenuListMobile) {
        const titleList: HTMLElement[] = [
            ...document.querySelectorAll<HTMLElement>('.rte h2'),
            ...document.querySelectorAll<HTMLElement>('.section__title h2'),
        ];
        // Height sticky menu
        const headerOffset = 145;

        titleList.forEach(item => {
            // Add class to title
            item.classList.add('js-sidebar-title');

            const clickRollContent = document.querySelector('.section.click-roll-content');
            if (clickRollContent) {
                clickRollContent.querySelectorAll('h2, h3, h4, h5, h6').forEach(h2 => {
                    h2.classList.remove('js-sidebar-title');
                });
            }
            // Create unique id for each title
            const uniqueId = getUniqId();
            const id = item.textContent?.trim().toLowerCase().replace(/\s+/g, '_') + '-' + uniqueId;
            if (id) {
                item.setAttribute('id', id);
            }
            // Add tabindex to title
            item.setAttribute('tabindex', '-1');
            // Create sidebar items desktop
            const listItemdesktop = document.createElement('li');
            addClass(listItemdesktop, 'sidebar-menu__list-item');
            listItemdesktop.innerHTML = `<a class="sidebar-menu__button" href="#${item.getAttribute('id')}">${item.textContent}</a>`;

            // Create sidebar items
            const listItemMobile = document.createElement('li');
            addClass(listItemMobile, 'sidebar-menu__list-item');
            listItemMobile.innerHTML = `<a class="sidebar-menu__button" href="#${item.getAttribute('id')}">${item.textContent}</a>`;

            // Sidebar behavior, on click scroll to item
            sideBarMenuListDesktop.append(listItemdesktop);
            sideBarMenuListMobile.append(listItemMobile);

        });

        setTimeout(() => {
            const menuItems = document.querySelectorAll<HTMLElement>('.sidebar-menu__button');
            menuItems.forEach(menuItem => {
                menuItem.addEventListener('click', (): any => {
                    const title = document.querySelector(`${menuItem.getAttribute('href')}`);
                    const y = title!.getBoundingClientRect().top + window.pageYOffset - headerOffset;
                    console.log(y);
                    console.log(headerOffset);
                    window.scrollTo({ top: y });
                });
            });
        });

        const footerWrapper = document.querySelector('.site-wrapper__footer') as HTMLElement;
        const sideBarOffset = sideBarMenuDesktop.getBoundingClientRect().top + window.pageYOffset - headerOffset;
        const _footerWrapperOffset = footerWrapper.getBoundingClientRect().top + window.pageYOffset - headerOffset - 70;

        // Add an active class when scrolling over an item
        const buttons = document.querySelectorAll<HTMLAnchorElement>('.sidebar-menu__button');
        buttons.forEach(button => {
            button.addEventListener('click', function (event: MouseEvent) {
                event.preventDefault();

                buttons.forEach(btn => btn.classList.remove('is-active'));
                this.classList.add('is-active');
            });
        });

        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (sideBarMenuDesktop) {
                if (scrollTop >= sideBarOffset && !sideBarMenuDesktop.classList.contains('is-fixed')) {
                    addClass(sideBarMenuDesktop, 'is-fixed');
                    sideBarMenuDesktop.style.top = `${headerOffset}px`;
                } else if (scrollTop < sideBarOffset && sideBarMenuDesktop.classList.contains('is-fixed')) {
                    removeClass(sideBarMenuDesktop, 'is-fixed');
                    sideBarMenuDesktop.style.top = '';
                }
            }

            const listItems = document.querySelectorAll<HTMLElement>('.is-desktop .sidebar-menu__list-item a');
            // Add an active class when scrolling over an item
            for (let i = titleList.length - 1; i >= 0; i--) {
                if (window.pageYOffset > titleList[i].getBoundingClientRect().top + window.pageYOffset - 600) {
                    listItems.forEach(item => {
                        if (hasClass(item, 'is-active')) {
                            removeClass(item, 'is-active');
                        }
                    });

                    addClass(listItems[i], 'is-active');
                    break;
                }
            }
        });
    }
};

export default createSidebarMenu;
