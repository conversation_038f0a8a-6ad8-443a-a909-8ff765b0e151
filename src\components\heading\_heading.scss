.heading-filter-toggle {
    @extend %button;
    @extend %button-size-small;
    @extend %button-style-only-icon;
    @extend %button-style-circle;
    @extend %button-style-secondary;

    @include breakpoint(medium) {
        display: none;
    }

    &.is-md-breakpoint {
        @include breakpoint(medium down) {
            display: block;
        }
    }

    &.is-lg-breakpoint {
        display: block;
    }
}

.heading {
    $this: &;

    &.is-center {
        #{$this}__category,
        #{$this}__title,
        #{$this}__teaser,
        #{$this}__name,
        #{$this}__synonyms,
        #{$this}__publication,
        #{$this}__type,
        #{$this}__function {
            text-align: center;
        }

        #{$this}__links,
        #{$this}__title {
            justify-content: center;
            width: 100%;
        }
    }

    &.has-image-left {
        #{$this}__image {
            max-width: 250px;
            order: -1;

            @include breakpoint(medium down) {
                margin-bottom: 25px;
            }

            @include breakpoint(small down) {
                max-width: 100%;
            }
        }

        #{$this}__title-text {
            &::before {
                left: -384px;
                width: 510px;

                @include breakpoint(small down) {
                    left: -427px;
                }
            }
        }

        #{$this}__content {
            @include breakpoint(medium only) {
                width: 1%;
            }
        }
    }

    &__wrapper {
        @extend %container;
        background-color: $color-white;
        display: flex;
        flex-wrap: wrap;
        padding: 0 40px 50px;

        @include breakpoint(medium down) {
            padding: 18px 62px 40px;
        }

        @include breakpoint(small down) {
            flex-direction: column;
            padding: 20px 20px 40px;
        }
    }

    &__image {
        margin: 0 56px 0 0;
        max-width: 510px;
        z-index: 3;

        @include breakpoint(medium down) {
            margin: 0 34px 0 0;
        }

        @include breakpoint(small down) {
            margin: 0 0 25px;
            max-width: 100%;
        }

        &.is-right {
            margin: -155px 0 0 70px;
            max-width: 510px;

            @include breakpoint(medium down) {
                margin: 50px 0 0;
                max-width: 100%;
            }
        }

        &.is-left {
            margin: 0;
            max-width: 439px;

            @include breakpoint(medium down) {
                margin: 50px 0 0;
                max-width: 100%;
            }
        }

        &.is-small {
            @include breakpoint(large) {
                max-width: 272px;
            }
        }

        picture {
            border-radius: 10px;
            display: block;

            &.is-rounded {
                border-radius: 50%;
                overflow: hidden;
            }

            img {
                border-radius: 10px;
            }
        }

        + .heading__content {
            width: 1%;

            @include breakpoint(small down) {
                width: 100%;
            }

            .heading__title-text::before,
            .heading__name-text::before {
                @include breakpoint(large only) {
                    left: -384px;
                    width: 510px;
                }
            }
        }

        #{$this}__status {
            > * {
                display: block;
                margin: 0 0 10px;
            }
        }
    }

    &__image-link {
        @include focus-outline($color: var(--color-1--1), $offset: 2px);
        color: $color-black;
        display: block;
        margin-top: 20px;
        position: relative;
        text-align: center;
        width: 100%;

        @include on-event {
            picture {
                //
            }
        }

        .is-complete & {
            &::before {
                @include absolute(0);
                @include font(var(--typo-1), 6rem, var(--fw-black));
                @include size(100%);
                align-items: center;
                background-color: rgba($color-black, 0.4);
                border-radius: 10px;
                color: $color-white;
                content: "Complet";
                display: flex;
                justify-content: center;
                letter-spacing: 4.86px;
                text-transform: uppercase;
                z-index: 1;

                @include breakpoint(medium down) {
                    height: 213px;

                    .single-event & {
                        height: 100%;
                    }
                }
            }
        }

        .is-canceled & {
            &::before {
                @include absolute(0);
                @include font(var(--typo-1), 6rem, var(--fw-black));
                @include size(100%);
                align-items: center;
                background-color: rgba($color-black, 0.4);
                border-radius: 10px;
                color: $color-white;
                content: "Annulé";
                display: flex;
                justify-content: center;
                letter-spacing: 4.86px;
                text-transform: uppercase;
                z-index: 1;

                @include breakpoint(medium down) {
                    height: 213px;
                }
            }
        }

        .is-reported & {
            &::before {
                @include absolute(0);
                @include font(var(--typo-1), 6rem, var(--fw-black));
                @include size(100%);
                align-items: center;
                background-color: rgba($color-black, 0.4);
                border-radius: 10px;
                color: $color-white;
                content: "Reporté";
                display: flex;
                justify-content: center;
                letter-spacing: 4.86px;
                text-transform: uppercase;
                z-index: 1;

                @include breakpoint(medium down) {
                    height: 213px;
                }
            }
        }
    }

    &__date {
        margin: 12px 0;
    }

    &__caption {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal), italic);
        color: $color-3--4;
        line-height: 1.35;
        margin-top: 15px;
    }

    &__content {
        align-content: flex-start;
        align-items: flex-start;
        display: flex;
        flex-grow: 2;
        flex-wrap: wrap;
        position: relative;
        width: 1%;

        @include breakpoint(medium down) {
            width: 100%;
        }

        > *:first-child {
            margin-top: 0;
        }

        > *:not(#{$this}__title):not(#{$this}__links) {
            width: 100%;
        }

        &.content-left {
            flex-grow: 1;
            margin: 0 47px 0 0;
            min-width: 439px;
        }

        &.is-full-width {
            width: 100%;
        }
    }

    &__content-top {
        margin-bottom: 30px;
        width: 100%;

        @include breakpoint(small down) {
            margin-bottom: 20px;
        }
    }

    &__content-bottom {
        width: 100%;

        &.is-elected,
        &.is-works-info,
        &.is-structures,
        &.is-contacts {
            background-color: $color-3--1;
            margin-top: 70px;
            padding: 65px 75px;
            width: 100%;

            @include breakpoint(large only) {
                margin-left: -80px;
                margin-right: -80px;
                width: calc(100% + 160px);
            }

            @include breakpoint(medium down) {
                padding: 40px 55px 25px;
            }

            @include breakpoint(small down) {
                padding: 25px;
            }

            .flex-row {
                > div {
                    align-items: flex-start;
                    display: flex;
                    flex-direction: column;

                    @include breakpoint(small down) {
                        align-items: center;
                    }

                    &:nth-child(1) {
                        @include breakpoint(medium down) {
                            margin: 0 0 40px;
                        }
                    }
                }
            }

            .list-contact {
                margin: -2.5px;

                @include breakpoint(medium down) {
                    display: flex;
                    flex-wrap: wrap;
                    margin-bottom: 15px;
                }

                @include breakpoint(small down) {
                    align-items: center;
                    flex-direction: column;
                }

                &__item {
                    padding: 2.5px;
                }
            }
        }

        &.is-contacts,
        &.is-elected {
            .flex-row {
                > div {
                    &:nth-child(2),
                    &:nth-child(3) {
                        @include breakpoint(medium only) {
                            max-width: 407px;
                        }
                    }
                }
            }
        }

        &.is-works-info {
            .flex-row {
                > div {
                    &:nth-child(1) {
                        margin-bottom: 0;

                        @include breakpoint(small down) {
                            align-items: flex-start;
                            margin: 0 auto;
                            max-width: 430px;
                        }
                    }

                    &:nth-child(2) {
                        @include breakpoint(medium only) {
                            max-width: 250px;
                        }

                        @include breakpoint(small down) {
                            align-items: flex-start;
                            margin: 0 auto;
                            max-width: 430px;
                        }
                    }

                    &:nth-child(3) {
                        @include breakpoint(medium only) {
                            max-width: 355px;
                        }
                    }

                    .list-contact {
                        @include breakpoint(medium down) {
                            margin-top: 10px;
                        }
                    }
                }
            }
        }

        &.is-structures {
            .flex-row {
                @include breakpoint(medium only) {
                    justify-content: space-between;
                }

                > div {
                    &:nth-child(3) {
                        @include breakpoint(medium only) {
                            align-items: center;
                            max-width: 250px;
                        }
                    }
                }
            }
        }
    }

    &__content-buttons {
        width: 100%;

        .btn.is-small {
            margin-top: 25px;
            padding: 1.93em 4em;
        }
    }

    &__places-numbers {
        color: $color-4--1;
        font-size: 1.3rem;
        letter-spacing: 1.3px;
        padding: 9px 22px;
        text-transform: uppercase;
    }

    &__content-btn {
        margin: 25px 5px 10px 0;
    }

    &__category {
        @include font(var(--typo-1), 1.8rem, var(--fw-black));
        color: $color-white;
        display: block;
        margin: 0 0 10px;
        text-transform: uppercase;

        @include breakpoint(small down) {
            font-size: 1.4rem;
        }
    }

    &__title,
    &__name {
        @include font(null, 4.5rem, var(--fw-normal));
        align-items: center;
        align-self: stretch;
        color: $color-white;
        flex-grow: 2;
        line-height: 1.25;
        margin: 0 0 5px;
        position: relative;
        z-index: 0;

        @include breakpoint(large only) {
            margin: 0 0 50px;
            max-width: 700px;
        }

        @include breakpoint(medium down) {
            font-size: 5.5rem;
            margin: 0 0 25px;
        }

        @include breakpoint(small down) {
            font-size: 3.8rem;
            line-height: 1.25;
            word-break: break-word;
        }

        &::before {
            @include absolute(-129px, null, null, 50%);
            @include size(1000vw, calc(100% + 156px));
            background: transparent linear-gradient(262deg, $color-1--1 46%, $color-4--3 57%) 0% 0% no-repeat padding-box;
            content: '';
            transform: translateX(-50%);
            z-index: -1;

            @include breakpoint(medium down) {
                height: calc(100% + 140px);
            }

            @include breakpoint(small down) {
                height: calc(100% + 113px);
                top: -100px;
            }
        }

        + #{$this}__publication {
            margin-bottom: 15px;
            margin-top: 0;
        }
    }

    &__title-text,
    &__name-text {
        display: block;
    }

    &__subtitle {
        @include font(null, 3.5rem, var(--fw-normal));

        @include breakpoint(medium down) {
            font-size: 2.5rem;
        }

        @include breakpoint(small down) {
            font-size: 2.2rem;
        }
    }

    &__events-group {
        align-items: center;
        display: flex;
        justify-content: space-between;

        #{$this}__date {
            margin: 0;

            @include breakpoint(small down) {
                width: 100%;
            }

            .date {
                @include breakpoint(small down) {
                    width: 100%;
                }
            }
        }

        #{$this}__time-place {
            flex: 2;
        }

        #{$this}__content-buttons {
            margin: 12px;
            width: auto;
        }
    }

    &__works-group {
        display: flex;

        @include breakpoint(small down) {
            flex-direction: column;
        }
    }

    &__links {
        @extend %button-links-group;
        align-self: stretch;
        flex-shrink: 0;
    }

    &__buttons {
        @extend %container;
        display: flex;
        gap: 0 10px;
    }

    &__synonyms {
        @include font(null, 2.4rem, var(--fw-bold));
        color: var(--color-1--1);
        margin: 0 0 5px;
    }

    &__publication {
        margin: 25px 0 0;

        &.is-large {
            margin: 35px 0 5px;

            @include breakpoint(small down) {
                margin: 20px 0 5px;
            }
        }
    }

    .is-single-elected & {
        @include breakpoint(small down) {
            &__content {
                order: 1;
            }

            &.has-image-left {
                #{$this}__image {
                    order: 2;
                }
            }

            &__content-bottom {
                order: 3;
            }
        }
    }

    &__function {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: $color-black;
        line-height: 1.55;
        margin: 0;

        &.is-main {
            @include font(null, 1.8rem, var(--fw-bold));
            color: var(--color-1--1);
            margin: 34px 0 0;
        }
    }

    &__teaser {
        + .heading__status {
            margin-top: 20px;
        }
    }

    &__quote {
        margin-top: 30px;
        position: relative;

        @include breakpoint(small down) {
            margin-top: 20px;
        }

        &::after {
            content: close-quote;
        }

        &::before {
            content: open-quote;
        }
    }

    &__content-state {
        align-items: center;
        border-bottom: 1px solid $color-3--4;
        border-top: 1px solid $color-3--4;
        display: flex;

        @include breakpoint(medium down) {
            align-items: flex-start;
            flex-direction: column;
        }
    }

    &__status {
        margin: 35px 0 0;

        > * {
            display: inline-block;
        }
    }

    .copyright {
        display: block;
    }
}
